"""
Integration Management Routes

This module provides API endpoints for managing integrations (admin only).
Integrations are dynamic OAuth providers and services that can be configured
without code changes.
"""

from typing import Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.responses import JSONResponse, RedirectResponse
from urllib.parse import quote_plus
from app.core.auth_guard import role_required
from app.schemas.integration import (
    IntegrationCreate,
    IntegrationUpdate,
    IntegrationResponse,
    IntegrationListResponse,
    IntegrationDeleteResponse,
    OAuthCredentialResponse,
    OAuthCredentialDeleteResponse,
    UserIntegrationsListResponse,
    UserIntegrationStatus,
    APIKeyCredentialRequest,
    APIKeyCredentialResponse,
    APIKeyCredentialStoreResponse,
    APIKeyCredentialUpdateResponse,
    APIKeyCredentialDeleteResponse,
)

from app.services.authentication_service import get_auth_service_client
import logging

from app.utils.parse_error import parse_error

logger = logging.getLogger(__name__)

# Initialize router
integration_router = APIRouter(prefix="/integrations", tags=["Integration Management"])

# Create a separate router for public endpoints (like OAuth callbacks)
public_integration_router = APIRouter(prefix="/integrations", tags=["Integration Management - Public"])


@integration_router.post(
    "",
    response_model=IntegrationResponse,
    summary="Create a new integration",
    description="""
    Create a new integration configuration (Admin only).
    
    This endpoint allows administrators to add new OAuth providers or services
    dynamically without requiring code changes.
    """,
    responses={
        201: {"description": "Integration created successfully"},
        400: {"description": "Bad Request - Invalid integration data"},
        401: {"description": "Unauthorized - Admin access required"},
        500: {"description": "Internal Server Error"},
    },
)
async def create_integration(
    integration_data: IntegrationCreate,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """Create a new integration."""
    try:
        admin_user_id = current_user.get("user_id")
        if not admin_user_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Admin user context missing ID",
            )

        logger.info(f"Creating integration: {integration_data.name} by admin: {admin_user_id}")

        # Get integration type
        integration_type = integration_data.integration_type.value if hasattr(integration_data.integration_type, 'value') else str(integration_data.integration_type)
        
        # Call authentication service to create integration
        result = await get_auth_service_client().create_integration(
            admin_user_id=admin_user_id,
            name=integration_data.name,
            display_name=integration_data.display_name,
            description=integration_data.description,
            integration_type=integration_type,
            is_enabled=integration_data.is_enabled,
            configuration=integration_data.configuration,
            supported_scopes=integration_data.supported_scopes,
            api_key_config=integration_data.api_key_config,
        )

        if result["success"]:
            logger.info(f"Successfully created integration: {result['integration_id']}")
            return JSONResponse(
                status_code=status.HTTP_201_CREATED,
                content={
                    "success": True,
                    "message": result["message"],
                    "integration_id": result["integration_id"],
                },
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"],
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error creating integration: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create integration",
        )


@integration_router.get(
    "/{integration_id}",
    response_model=IntegrationResponse,
    summary="Get integration by ID",
    description="""
    Retrieve a specific integration by its ID (Admin only).
    """,
    responses={
        200: {"description": "Integration retrieved successfully"},
        401: {"description": "Unauthorized - Admin access required"},
        404: {"description": "Integration not found"},
        500: {"description": "Internal Server Error"},
    },
)
async def get_integration(
    integration_id: str,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """Get integration by ID."""
    try:
        logger.info(f"Retrieving integration: {integration_id}")

        # Call authentication service to get integration
        result = await get_auth_service_client().get_integration(integration_id)

        if result["success"]:
            integration_data = result["integration"]
            return IntegrationResponse(
                id=integration_data["id"],
                name=integration_data["name"],
                description=integration_data["description"],
                integration_type=integration_data["integration_type"],
                is_enabled=integration_data["is_enabled"],
                status=integration_data["status"],
                configuration=integration_data["configuration"],
                supported_scopes=integration_data["supported_scopes"],
                created_at=integration_data["created_at"],
                updated_at=integration_data["updated_at"],
                api_key_config=integration_data["api_key_config"],
            )
        else:
            if "not found" in result["message"].lower():
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=result["message"],
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=result["message"],
                )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error retrieving integration: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve integration",
        )


@integration_router.put(
    "/{integration_id}",
    response_model=IntegrationResponse,
    summary="Update integration",
    description="""
    Update an existing integration configuration (Admin only).
    """,
    responses={
        200: {"description": "Integration updated successfully"},
        400: {"description": "Bad Request - Invalid integration data"},
        401: {"description": "Unauthorized - Admin access required"},
        404: {"description": "Integration not found"},
        500: {"description": "Internal Server Error"},
    },
)
async def update_integration(
    integration_id: str,
    integration_data: IntegrationUpdate,
    current_user: dict = Depends(role_required(["admin"])),
):
    """Update an existing integration."""
    try:
        admin_user_id = current_user.get("user_id")
        if not admin_user_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Admin user context missing ID",
            )

        logger.info(f"Updating integration: {integration_id} by admin: {admin_user_id}")

        # Call authentication service to update integration
        result = await get_auth_service_client().update_integration(
            admin_user_id=admin_user_id,
            integration_id=integration_id,
            display_name=integration_data.display_name,
            description=integration_data.description,
            is_enabled=integration_data.is_enabled,
            configuration=integration_data.configuration,
            supported_scopes=integration_data.supported_scopes,
            api_key_config=integration_data.api_key_config,
        )

        if result["success"]:
            integration_data = result["integration"]
            return IntegrationResponse(
                id=integration_data["id"],
                name=integration_data["name"],
                description=integration_data["description"],
                integration_type=integration_data["integration_type"],
                is_enabled=integration_data["is_enabled"],
                status=integration_data["status"],
                configuration=integration_data["configuration"],
                supported_scopes=integration_data["supported_scopes"],
                created_at=integration_data["created_at"],
                updated_at=integration_data["updated_at"],
            )
        else:
            if "not found" in result["message"].lower():
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=result["message"],
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=result["message"],
                )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error updating integration: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update integration",
        )


@integration_router.delete(
    "/{integration_id}",
    response_model=IntegrationDeleteResponse,
    summary="Delete integration",
    description="""
    Delete an integration configuration (Admin only).
    
    This will remove the integration and all associated configurations.
    """,
    responses={
        200: {"description": "Integration deleted successfully"},
        401: {"description": "Unauthorized - Admin access required"},
        404: {"description": "Integration not found"},
        500: {"description": "Internal Server Error"},
    },
)
async def delete_integration(
    integration_id: str,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """Delete an integration."""
    try:
        admin_user_id = current_user.get("user_id")
        if not admin_user_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Admin user context missing ID",
            )

        logger.info(f"Deleting integration: {integration_id} by admin: {admin_user_id}")

        # Call authentication service to delete integration
        result = await get_auth_service_client().delete_integration(
            admin_user_id=admin_user_id,
            integration_id=integration_id,
        )

        if result["success"]:
            return IntegrationDeleteResponse(
                success=True,
                message=result["message"],
            )
        else:
            if "not found" in result["message"].lower():
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=result["message"],
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=result["message"],
                )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error deleting integration: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete integration",
        )


@integration_router.get(
    "",
    response_model=IntegrationListResponse,
    summary="List integrations",
    description="""
    List all integrations with optional filtering (Admin only).
    
    Supports pagination and filtering by provider, integration type, and enabled status.
    """,
    responses={
        200: {"description": "Integrations retrieved successfully"},
        401: {"description": "Unauthorized - Admin access required"},
        500: {"description": "Internal Server Error"},
    },
)
async def list_integrations(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Items per page"),
    provider: Optional[str] = Query(None, description="Filter by provider"),
    integration_type: Optional[str] = Query(None, description="Filter by integration type"),
    is_enabled: Optional[bool] = Query(None, description="Filter by enabled status"),
    include_inactive: bool = Query(False, description="Include inactive integrations"),
    connection_type: Optional[str] = Query(
        None, description="Filter by connection type (api_key, oauth)"
    ),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """List integrations with optional filters and pagination."""
    try:
        logger.info(
            f"Listing integrations - page: {page}, page_size: {page_size}, filters: provider={provider}, type={integration_type}, enabled={is_enabled}"
        )

        # Call authentication service to list integrations
        result = await get_auth_service_client().list_integrations(
            page=page,
            page_size=page_size,
            provider=provider,
            integration_type=integration_type,
            is_enabled=is_enabled,
            include_inactive=include_inactive,
            connection_type_filter=connection_type,
        )

        if result["success"]:
            integrations = []
            for integration_data in result["integrations"]:
                integrations.append(
                    IntegrationResponse(
                        id=integration_data["id"],
                        name=integration_data["name"],
                        description=integration_data["description"],
                        integration_type=integration_data["integration_type"],
                        is_enabled=integration_data["is_enabled"],
                        status=integration_data["status"],
                        configuration=integration_data["configuration"],
                        api_key_config=integration_data["api_key_config"],
                        supported_scopes=integration_data["supported_scopes"],
                        created_at=integration_data["created_at"],
                        updated_at=integration_data["updated_at"],
                    )
                )

            return IntegrationListResponse(
                success=True,
                message=result["message"],
                integrations=integrations,
                total=result.get("total", len(integrations)),
                page=result.get("page", page),
                page_size=result.get("page_size", page_size),
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result["message"],
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error listing integrations: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list integrations",
        )


# User-specific OAuth Integration Routes


@integration_router.get(
    "/{integration_id}/oauth/authorize",
    summary="Initiate OAuth authorization for integration",
    description="""
    Initiate OAuth authorization flow for a specific integration (User access).
    
    This endpoint allows users to start the OAuth flow for any enabled integration.
    The user will be redirected to the provider's authorization page.
    """,
    responses={
        302: {"description": "Redirect to OAuth provider authorization URL"},
        400: {"description": "Bad Request - Invalid integration or parameters"},
        401: {"description": "Unauthorized - User authentication required"},
        404: {"description": "Integration not found or not enabled"},
        500: {"description": "Internal Server Error"},
    },
)
async def initiate_oauth_by_integration(
    integration_id: str,
    redirect_url: Optional[str] = Query(
        None, description="Optional redirect URL after OAuth completion"
    ),
    user_id: str = Query(..., description="The user ID"),
):
    """Initiate OAuth authorization flow for a specific integration."""
    try:
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="User context missing ID",
            )

        logger.info(f"Initiating OAuth for integration: {integration_id} by user: {user_id}")

        # Call authentication service to initiate OAuth by integration
        result = await get_auth_service_client().initiate_oauth_by_integration(
            user_id=user_id,
            integration_id=integration_id,
            redirect_url=redirect_url,
        )

        if result["success"]:
            print(f"[DEBUG] recieved authorization_url")
            # Redirect user to OAuth provider's authorization URL
            return RedirectResponse(
                url=result["authorization_url"],
                status_code=status.HTTP_302_FOUND,
            )
        else:
            if "not found" in result["message"].lower():
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=result["message"],
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=result["message"],
                )

    except HTTPException:
        raise
    except Exception as e:
        print(f"[DEBUG] Unexpected error initiating OAuth for integration: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to initiate OAuth authorization",
        )


@integration_router.get(
    "/{integration_id}/oauth/credentials",
    response_model=OAuthCredentialResponse,
    summary="Get OAuth credentials for integration",
    description="""
    Retrieve OAuth credentials for a specific integration (User access).
    
    This endpoint allows users to get their stored OAuth credentials for an integration.
    Returns access token and related OAuth information.
    """,
    responses={
        200: {"description": "OAuth credentials retrieved successfully"},
        401: {"description": "Unauthorized - User authentication required"},
        404: {"description": "Integration not found or no credentials stored"},
        500: {"description": "Internal Server Error"},
    },
)
async def get_oauth_credentials_by_integration(
    integration_id: str,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """Get OAuth credentials for a specific integration."""
    try:
        user_id = current_user.get("user_id")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="User context missing ID",
            )

        logger.info(
            f"Getting OAuth credentials for integration: {integration_id} by user: {user_id}"
        )

        # Call authentication service to get OAuth credentials by integration
        result = await get_auth_service_client().get_oauth_credentials_by_integration(
            user_id=user_id,
            integration_id=integration_id,
        )

        if not result["success"]:
           print(f"[ERROR] Error retrieving OAuth credentials: {result['message']}")
           raise HTTPException(
               status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
               detail=result["message"],
           )

        # Convert user_integration_status to UserIntegrationStatus object if present
        user_integration_status = None
        if result.get("user_integration_status"):
            status_data = result["user_integration_status"]
            user_integration_status = UserIntegrationStatus(
                user_id=status_data["user_id"],
                integration_id=status_data["integration_id"],
                integration_name=status_data["integration_name"],
                is_connected=status_data["is_connected"],
                last_used_at=status_data.get("last_used_at"),
                created_at=status_data["created_at"],
                scopes=status_data.get("scopes", []),
                connection_type=status_data["connection_type"],
                schema_definition=status_data.get("schema_definition")
            )

        return OAuthCredentialResponse(
            success=True,
            message=result["message"],
            access_token=result["access_token"],
            token_type=result.get("token_type", "Bearer"),
            expires_in=result.get("expires_in", 3600),
            scope=result.get("scope", ""),
            user_integration_status=user_integration_status,
        )
        # else:
        #     if "not found" in result["message"].lower():
        #         raise HTTPException(
        #             status_code=status.HTTP_404_NOT_FOUND,
        #             detail=result["message"],
        #         )
        #     else:
        #         raise HTTPException(
        #             status_code=status.HTTP_400_BAD_REQUEST,
        #             detail=result["message"],
        #         )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Unexpected error getting OAuth credentials for integration: {str(e)}", exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve OAuth credentials",
        )


@integration_router.delete(
    "/{integration_id}/oauth/credentials",
    response_model=OAuthCredentialDeleteResponse,
    summary="Delete OAuth credentials for integration",
    description="""
    Delete OAuth credentials for a specific integration (User access).
    
    This endpoint allows users to revoke and delete their stored OAuth credentials
    for an integration. This will disconnect the integration for the user.
    """,
    responses={
        200: {"description": "OAuth credentials deleted successfully"},
        401: {"description": "Unauthorized - User authentication required"},
        404: {"description": "Integration not found or no credentials stored"},
        500: {"description": "Internal Server Error"},
    },
)
async def delete_oauth_credentials_by_integration(
    integration_id: str,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """Delete OAuth credentials for a specific integration."""
    try:
        user_id = current_user.get("user_id")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="User context missing ID",
            )

        logger.info(
            f"Deleting OAuth credentials for integration: {integration_id} by user: {user_id}"
        )

        # Call authentication service to delete OAuth credentials by integration
        result = await get_auth_service_client().delete_oauth_credentials_by_integration(
            user_id=user_id,
            integration_id=integration_id,
        )

        if result["success"]:
            logger.info(f"Successfully deleted OAuth credentials for integration: {integration_id}")
            return OAuthCredentialDeleteResponse(
                success=True,
                message=result["message"],
            )
        else:
            if "not found" in result["message"].lower():
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=result["message"],
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=result["message"],
                )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Unexpected error deleting OAuth credentials for integration: {str(e)}", exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete OAuth credentials",
        )


@integration_router.get(
    "/user/connected",
    response_model=UserIntegrationsListResponse,
    summary="List user's connected integrations",
    description="""
    List all integrations connected by the current user (User access).
    
    This endpoint returns all integrations that the user has authorized
    and connected via OAuth, along with their connection status.
    """,
    responses={
        200: {"description": "User integrations retrieved successfully"},
        401: {"description": "Unauthorized - User authentication required"},
        500: {"description": "Internal Server Error"},
    },
)
async def list_user_connected_integrations(
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """List user's connected integrations."""
    try:
        user_id = current_user.get("user_id")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="User context missing ID",
            )

        logger.info(f"Listing connected integrations for user: {user_id}")

        # Call authentication service to list user integrations
        result = await get_auth_service_client().list_user_integrations(user_id=user_id)

        if isinstance(result, list):
            # Success case - result is a list of integrations
            logger.info(
                f"Successfully retrieved {len(result)} connected integrations for user: {user_id}"
            )

            # Convert dictionaries to UserIntegrationStatus objects
            integrations = []
            for integration_data in result:
                integration = UserIntegrationStatus(
                    user_id=integration_data["user_id"],
                    integration_id=integration_data["integration_id"],
                    integration_name=integration_data["integration_name"],
                    is_connected=integration_data["is_connected"],
                    last_used_at=integration_data.get("last_used_at"),
                    created_at=integration_data["created_at"],
                    scopes=integration_data.get("scopes", []),
                    connection_type=integration_data["connection_type"],
                    schema_definition=integration_data.get("schema_definition")
                )
                integrations.append(integration)

            return UserIntegrationsListResponse(
                success=True,
                message=f"Retrieved {len(integrations)} connected integrations",
                integrations=integrations,
            )
        elif isinstance(result, dict) and not result.get("success", True):
            # Error case - result is an error dict
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.get("message", "Failed to list user integrations"),
            )
        else:
            # Unexpected result format
            logger.warning(f"Unexpected result format from list_user_integrations: {type(result)}")
            return UserIntegrationsListResponse(
                success=True,
                message="No connected integrations found",
                integrations=[],
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Unexpected error listing user connected integrations: {str(e)}", exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list connected integrations",
        )


@integration_router.get(
    "/callback",
    summary="Handle OAuth callback",
    description="""
    Handle OAuth callback from any supported provider.

    This endpoint:
    - Validates the state parameter
    - Exchanges authorization code for tokens
    - Stores credentials securely
    - Works with any configured OAuth provider
    - Optionally redirects to a custom URL after processing (if provided during authorization)

    If redirect_url was provided during the authorization request, the user will be redirected to that URL with query parameters:
    - success=true/false
    - message=<result_message>
    - provider=<oauth_provider> (on success)
    - tool_name=<tool_name> (on success)
    - error=<error_code> (on failure)
    """,
    responses={
        200: {"description": "OAuth flow completed successfully"},
        302: {"description": "Redirect to custom URL (when redirect_url is provided)"},
        400: {"description": "Bad Request - Invalid callback parameters"},
        500: {"description": "Internal Server Error"},
    },
)

async def oauth_callback(
    code: Optional[str] = Query(None, description="Authorization code from OAuth provider"),
    state: str = Query(..., description="State parameter from OAuth provider"),
    error: Optional[str] = Query(None, description="Error code if authorization failed"),
):
    """Handle OAuth callback from any provider."""
    try:
        # Call authentication service to handle callback first to get redirect_url
        result = await get_auth_service_client().handle_oauth_callback_new(
            code=code, state=state, error=error
        )

        # Extract redirect_url from the authentication service response
        redirect_url = result.get("redirect_url")

        # Handle OAuth errors
        if error:
            logger.error(f"OAuth error received in callback: {error}")
            user_message = f"Authorization failed: {error}"
            if error == "access_denied":
                user_message = "You denied the authorization request."

            # If redirect_url is provided, redirect with error parameters
            if redirect_url:
                error_redirect_url = f"{redirect_url}?success=false&error={quote_plus(error)}&message={quote_plus(user_message)}"
                logger.info(f"Redirecting to error URL: {error_redirect_url}")
                return RedirectResponse(url=error_redirect_url, status_code=status.HTTP_302_FOUND)

            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content={"success": False, "message": user_message},
            )

        if not code:
            logger.error("Callback received without code or error parameter.")
            error_message = "Missing authorization code in callback."

            # If redirect_url is provided, redirect with error parameters
            if redirect_url:
                error_redirect_url = f"{redirect_url}?success=false&error=missing_code&message={quote_plus(error_message)}"
                logger.info(f"Redirecting to error URL: {error_redirect_url}")
                return RedirectResponse(url=error_redirect_url, status_code=status.HTTP_302_FOUND)

            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content={"success": False, "message": error_message},
            )

        # Process the result from authentication service
        if result["success"]:
            logger.info(
                f"OAuth callback handled successfully for {result.get('provider', 'unknown')}"
            )

            # If redirect_url is provided, redirect with success parameters
            if redirect_url:
                success_redirect_url = (
                    f"{redirect_url}?success=true&message={quote_plus(result['message'])}"
                    f"&provider={quote_plus(result.get('provider', ''))}&tool_name={quote_plus(result.get('tool_name', ''))}"
                )
                logger.info(f"Redirecting to success URL: {success_redirect_url}")
                return RedirectResponse(url=success_redirect_url, status_code=status.HTTP_302_FOUND)

            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={
                    "success": True,
                    "message": result["message"],
                    "provider": result.get("provider"),
                    "tool_name": result.get("tool_name"),
                },
            )
        else:
            logger.error(f"OAuth callback failed: {result['message']}")

            # If redirect_url is provided, redirect with failure parameters
            if redirect_url:
                failure_redirect_url = f"{redirect_url}?success=false&error=callback_failed&message={quote_plus(result['message'])}"
                logger.info(f"Redirecting to failure URL: {failure_redirect_url}")
                return RedirectResponse(url=failure_redirect_url, status_code=status.HTTP_302_FOUND)

            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content={"success": False, "message": result["message"]},
            )

    except Exception as e:
        logger.error(f"Unexpected error in oauth_callback: {str(e)}", exc_info=True)
        error_message = "An internal error occurred during OAuth callback."

        # If redirect_url is provided, redirect with error parameters
        if redirect_url:
            exception_redirect_url = f"{redirect_url}?success=false&error=internal_error&message={quote_plus(error_message)}"
            logger.info(f"Redirecting to exception URL: {exception_redirect_url}")
            return RedirectResponse(url=exception_redirect_url, status_code=status.HTTP_302_FOUND)

        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "success": False,
                "message": error_message,
            },
        )


# API Key Credential Management Routes


@integration_router.post(
    "/{integration_id}/api-key/credentials",
    response_model=APIKeyCredentialStoreResponse,
    summary="Store API key credentials for integration",
    description="""
    Store API key credentials for a specific integration (User access).
    
    This endpoint allows users to store their API key credentials for an integration
    that uses API key authentication instead of OAuth.
    """,
    responses={
        201: {"description": "API key credentials stored successfully"},
        400: {"description": "Bad Request - Invalid credentials or integration"},
        401: {"description": "Unauthorized - User authentication required"},
        404: {"description": "Integration not found or not API key type"},
        500: {"description": "Internal Server Error"},
    },
)
async def store_api_key_credentials(
    integration_id: str,
    credential_data: APIKeyCredentialRequest,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """Store API key credentials for a specific integration."""
    try:
        user_id = current_user.get("user_id")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="User context missing ID",
            )

        logger.info(
            f"Storing API key credentials for integration: {integration_id} by user: {user_id}"
        )

        # Call authentication service to store API key credentials
        result = await get_auth_service_client().store_api_key_credentials(
            user_id=user_id,
            integration_id=integration_id,
            credentials=credential_data.credentials,
        )

        if result["success"]:
            logger.info(
                f"Successfully stored API key credentials for integration: {integration_id}"
            )
            return JSONResponse(
                status_code=status.HTTP_201_CREATED,
                content={
                    "success": True,
                    "message": result["message"],
                    "integration_id": result["integration_id"],
                },
            )
        else:
            if "not found" in result["message"].lower():
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=result["message"],
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=result["message"],
                )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Unexpected error storing API key credentials for integration: {str(e)}", exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to store API key credentials",
        )


@integration_router.get(
    "/{integration_id}/api-key/credentials",
    response_model=APIKeyCredentialResponse,
    summary="Get API key credentials for integration",
    description="""
    Retrieve API key credentials for a specific integration (User access).
    
    This endpoint allows users to get their stored API key credentials for an integration.
    Returns the decrypted credentials and connection status.
    """,
    responses={
        200: {"description": "API key credentials retrieved successfully"},
        401: {"description": "Unauthorized - User authentication required"},
        404: {"description": "Integration not found or no credentials stored"},
        500: {"description": "Internal Server Error"},
    },
)
async def get_api_key_credentials(
    integration_id: str,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """Get API key credentials for a specific integration."""
    try:
        user_id = current_user.get("user_id")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="User context missing ID",
            )

        logger.info(
            f"Getting API key credentials for integration: {integration_id} by user: {user_id}"
        )

        # Call authentication service to get API key credentials
        result = await get_auth_service_client().get_api_key_credentials(
            user_id=user_id,
            integration_id=integration_id,
        )
        
        if not result["success"]:
            print(f"[ERROR] Error retrieving API key credentials: {result['message']}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result["message"],
            )

        # if not result["success"]:
        #     print(f"[ERROR] Error retrieving API key credentials: {result['message']}")
        #     raise HTTPException(
        #         status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        #         detail=result["message"],
        #     )
        return APIKeyCredentialResponse(
            success=True,
            message=result["message"],
            user_id=result["user_id"],
            integration_id=result["integration_id"],
            credentials=result["credentials"],
            is_connected=result["is_connected"],
            last_used_at=result.get("last_used_at"),
            api_key_config=result.get("schema_definition"),
        )
        
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[ERROR] Unexpected error in get_api_key_credentials: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])



@integration_router.put(
    "/{integration_id}/api-key/credentials",
    response_model=APIKeyCredentialUpdateResponse,
    summary="Update API key credentials for integration",
    description="""
    Update API key credentials for a specific integration (User access).
    
    This endpoint allows users to update their stored API key credentials for an integration.
    The new credentials will replace the existing ones.
    """,
    responses={
        200: {"description": "API key credentials updated successfully"},
        400: {"description": "Bad Request - Invalid credentials or integration"},
        401: {"description": "Unauthorized - User authentication required"},
        404: {"description": "Integration not found or no credentials stored"},
        500: {"description": "Internal Server Error"},
    },
)
async def update_api_key_credentials(
    integration_id: str,
    credential_data: APIKeyCredentialRequest,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """Update API key credentials for a specific integration."""
    try:
        user_id = current_user.get("user_id")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="User context missing ID",
            )

        logger.info(
            f"Updating API key credentials for integration: {integration_id} by user: {user_id}"
        )

        # Call authentication service to update API key credentials
        result = await get_auth_service_client().update_api_key_credentials(
            user_id=user_id,
            integration_id=integration_id,
            credentials=credential_data.credentials,
        )

        if result["success"]:
            logger.info(
                f"Successfully updated API key credentials for integration: {integration_id}"
            )
            return APIKeyCredentialUpdateResponse(
                success=True,
                message=result["message"],
                integration_id=result["integration_id"],
            )
        else:
            if "not found" in result["message"].lower():
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=result["message"],
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=result["message"],
                )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Unexpected error updating API key credentials for integration: {str(e)}", exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update API key credentials",
        )


@integration_router.delete(
    "/{integration_id}/api-key/credentials",
    response_model=APIKeyCredentialDeleteResponse,
    summary="Delete API key credentials for integration",
    description="""
    Delete API key credentials for a specific integration (User access).
    
    This endpoint allows users to delete their stored API key credentials for an integration.
    This will disconnect the integration for the user.
    """,
    responses={
        200: {"description": "API key credentials deleted successfully"},
        401: {"description": "Unauthorized - User authentication required"},
        404: {"description": "Integration not found or no credentials stored"},
        500: {"description": "Internal Server Error"},
    },
)
async def delete_api_key_credentials(
    integration_id: str,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """Delete API key credentials for a specific integration."""
    try:
        user_id = current_user.get("user_id")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="User context missing ID",
            )

        logger.info(
            f"Deleting API key credentials for integration: {integration_id} by user: {user_id}"
        )

        # Call authentication service to delete API key credentials
        result = await get_auth_service_client().delete_oauth_credentials_by_integration(
            user_id=user_id,
            integration_id=integration_id,
        )

        if result["success"]:
            logger.info(
                f"Successfully deleted API key credentials for integration: {integration_id}"
            )
            return APIKeyCredentialDeleteResponse(
                success=True,
                message=result["message"],
            )
        else:
            if "not found" in result["message"].lower():
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=result["message"],
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=result["message"],
                )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Unexpected error deleting API key credentials for integration: {str(e)}", exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete API key credentials",
        )
